import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../theme';
import { LoadingSpinner } from '../../components/common';
import { providerServiceAPI } from '../../services/api';

const { width } = Dimensions.get('window');

interface DashboardData {
  provider_info: {
    business_name: string;
    is_verified: boolean;
    rating: number;
    total_bookings: number;
  };
  service_stats: {
    total_services: number;
    active_services: number;
    inactive_services: number;
    popular_services: number;
    service_limit: number | null;
    services_remaining: number | null;
  };
  revenue_stats: {
    total_revenue: number;
    average_service_price: number;
  };
  recent_services: Array<{
    id: string;
    name: string;
    base_price: number;
    is_available: boolean;
    booking_count: number;
  }>;
  top_services: Array<{
    id: string;
    name: string;
    base_price: number;
    booking_count: number;
  }>;
}

export const ProviderDashboardScreen: React.FC = () => {
  const navigation = useNavigation();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchDashboardData = async () => {
    try {
      const response = await providerServiceAPI.getDashboardSummary();
      setDashboardData(response.data);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchDashboardData();
  }, []);

  useFocusEffect(
    useCallback(() => {
      fetchDashboardData();
    }, [])
  );

  const navigateToServices = () => {
    navigation.navigate('ProviderServices' as never);
  };

  const navigateToAddService = () => {
    navigation.navigate('AddService' as never);
  };

  const navigateToBookings = () => {
    navigation.navigate('ProviderBookings' as never);
  };

  const navigateToAnalytics = () => {
    navigation.navigate('ProviderAnalytics' as never);
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!dashboardData) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={48} color={colors.error} />
          <Text style={styles.errorText}>Failed to load dashboard</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchDashboardData}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const { provider_info, service_stats, revenue_stats, recent_services, top_services } = dashboardData;

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.welcomeText}>Welcome back,</Text>
            <Text style={styles.businessName}>{provider_info.business_name}</Text>
          </View>
          <View style={styles.verificationBadge}>
            <Icon
              name={provider_info.is_verified ? 'verified' : 'pending'}
              size={20}
              color={provider_info.is_verified ? colors.success : colors.warning}
            />
            <Text style={[
              styles.verificationText,
              { color: provider_info.is_verified ? colors.success : colors.warning }
            ]}>
              {provider_info.is_verified ? 'Verified' : 'Pending'}
            </Text>
          </View>
        </View>

        {/* Quick Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Icon name="star" size={24} color={colors.primary} />
            <Text style={styles.statValue}>{provider_info.rating.toFixed(1)}</Text>
            <Text style={styles.statLabel}>Rating</Text>
          </View>
          <View style={styles.statCard}>
            <Icon name="event" size={24} color={colors.secondary} />
            <Text style={styles.statValue}>{provider_info.total_bookings}</Text>
            <Text style={styles.statLabel}>Bookings</Text>
          </View>
          <View style={styles.statCard}>
            <Icon name="room-service" size={24} color={colors.accent} />
            <Text style={styles.statValue}>{service_stats.active_services}</Text>
            <Text style={styles.statLabel}>Active Services</Text>
          </View>
          <View style={styles.statCard}>
            <Icon name="attach-money" size={24} color={colors.success} />
            <Text style={styles.statValue}>${revenue_stats.total_revenue.toFixed(0)}</Text>
            <Text style={styles.statLabel}>Revenue</Text>
          </View>
        </View>

        {/* Service Overview */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Service Overview</Text>
            <TouchableOpacity onPress={navigateToServices}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.serviceOverview}>
            <View style={styles.serviceOverviewItem}>
              <Text style={styles.serviceOverviewValue}>{service_stats.total_services}</Text>
              <Text style={styles.serviceOverviewLabel}>Total Services</Text>
            </View>
            <View style={styles.serviceOverviewItem}>
              <Text style={styles.serviceOverviewValue}>{service_stats.active_services}</Text>
              <Text style={styles.serviceOverviewLabel}>Active</Text>
            </View>
            <View style={styles.serviceOverviewItem}>
              <Text style={styles.serviceOverviewValue}>{service_stats.inactive_services}</Text>
              <Text style={styles.serviceOverviewLabel}>Inactive</Text>
            </View>
            <View style={styles.serviceOverviewItem}>
              <Text style={styles.serviceOverviewValue}>{service_stats.popular_services}</Text>
              <Text style={styles.serviceOverviewLabel}>Popular</Text>
            </View>
          </View>

          {service_stats.service_limit && (
            <View style={styles.serviceLimitInfo}>
              <Icon name="info" size={16} color={colors.warning} />
              <Text style={styles.serviceLimitText}>
                {service_stats.services_remaining} of {service_stats.service_limit} services remaining
              </Text>
            </View>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.actionButton} onPress={navigateToAddService}>
              <Icon name="add" size={24} color={colors.white} />
              <Text style={styles.actionButtonText}>Add Service</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} onPress={navigateToBookings}>
              <Icon name="event" size={24} color={colors.white} />
              <Text style={styles.actionButtonText}>View Bookings</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} onPress={navigateToAnalytics}>
              <Icon name="analytics" size={24} color={colors.white} />
              <Text style={styles.actionButtonText}>Analytics</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Services */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Services</Text>
            <TouchableOpacity onPress={navigateToServices}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          
          {recent_services.map((service) => (
            <View key={service.id} style={styles.serviceItem}>
              <View style={styles.serviceInfo}>
                <Text style={styles.serviceName}>{service.name}</Text>
                <Text style={styles.servicePrice}>${service.base_price.toFixed(2)}</Text>
              </View>
              <View style={styles.serviceStats}>
                <Text style={styles.serviceBookings}>{service.booking_count} bookings</Text>
                <View style={[
                  styles.serviceStatus,
                  { backgroundColor: service.is_available ? colors.success : colors.error }
                ]}>
                  <Text style={styles.serviceStatusText}>
                    {service.is_available ? 'Active' : 'Inactive'}
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>

        {/* Top Performing Services */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Top Performing Services</Text>
          
          {top_services.map((service, index) => (
            <View key={service.id} style={styles.topServiceItem}>
              <View style={styles.topServiceRank}>
                <Text style={styles.topServiceRankText}>{index + 1}</Text>
              </View>
              <View style={styles.topServiceInfo}>
                <Text style={styles.topServiceName}>{service.name}</Text>
                <Text style={styles.topServiceBookings}>{service.booking_count} bookings</Text>
              </View>
              <Text style={styles.topServicePrice}>${service.base_price.toFixed(2)}</Text>
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  welcomeText: {
    ...typography.body,
    color: colors.textSecondary,
  },
  businessName: {
    ...typography.h2,
    color: colors.textPrimary,
    marginTop: spacing.xs,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 16,
  },
  verificationText: {
    ...typography.caption,
    marginLeft: spacing.xs,
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    padding: spacing.lg,
    backgroundColor: colors.white,
    marginTop: spacing.sm,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: spacing.md,
  },
  statValue: {
    ...typography.h3,
    color: colors.textPrimary,
    marginTop: spacing.xs,
  },
  statLabel: {
    ...typography.caption,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  section: {
    backgroundColor: colors.white,
    marginTop: spacing.sm,
    padding: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    ...typography.h3,
    color: colors.textPrimary,
  },
  viewAllText: {
    ...typography.body,
    color: colors.primary,
    fontWeight: '600',
  },
  serviceOverview: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  serviceOverviewItem: {
    alignItems: 'center',
  },
  serviceOverviewValue: {
    ...typography.h2,
    color: colors.primary,
  },
  serviceOverviewLabel: {
    ...typography.caption,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  serviceLimitInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.md,
    padding: spacing.sm,
    backgroundColor: colors.warningLight,
    borderRadius: 8,
  },
  serviceLimitText: {
    ...typography.caption,
    color: colors.warning,
    marginLeft: spacing.xs,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.md,
  },
  actionButton: {
    flex: 1,
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: spacing.xs,
  },
  actionButtonText: {
    ...typography.caption,
    color: colors.white,
    marginTop: spacing.xs,
    fontWeight: '600',
  },
  serviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    ...typography.body,
    color: colors.textPrimary,
    fontWeight: '600',
  },
  servicePrice: {
    ...typography.caption,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  serviceStats: {
    alignItems: 'flex-end',
  },
  serviceBookings: {
    ...typography.caption,
    color: colors.textSecondary,
  },
  serviceStatus: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    marginTop: spacing.xs,
  },
  serviceStatusText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '600',
  },
  topServiceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  topServiceRank: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  topServiceRankText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '700',
  },
  topServiceInfo: {
    flex: 1,
  },
  topServiceName: {
    ...typography.body,
    color: colors.textPrimary,
    fontWeight: '600',
  },
  topServiceBookings: {
    ...typography.caption,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  topServicePrice: {
    ...typography.body,
    color: colors.primary,
    fontWeight: '600',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  errorText: {
    ...typography.body,
    color: colors.error,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
    marginTop: spacing.lg,
  },
  retryButtonText: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
});
