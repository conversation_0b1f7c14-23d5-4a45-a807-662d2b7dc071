/**
 * WelcomeScreen Component
 * 
 * The first screen users see when opening the app for the first time.
 * Provides app introduction and entry point to the onboarding flow.
 */

import React from 'react';
import {
  View,
  StyleSheet,
  Image,
  Dimensions,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';

import { Text } from '../../components/ui/Text';
import { Button } from '../../components/ui/Button';
import { colors, spacing, typography } from '../../theme';

const { width, height } = Dimensions.get('window');

type WelcomeScreenNavigationProp = StackNavigationProp<any, 'Welcome'>;

export const WelcomeScreen: React.FC = () => {
  const navigation = useNavigation<WelcomeScreenNavigationProp>();

  const handleGetStarted = () => {
    navigation.navigate('Initialization');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary} />
      
      {/* Header Section */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          {/* App Logo Placeholder */}
          <View style={styles.logoPlaceholder}>
            <Text variant="h1" color="white" style={styles.logoText}>
              V
            </Text>
          </View>
          
          <Text variant="h1" color="white" style={styles.appName}>
            Vierla
          </Text>
          
          <Text variant="body" color="white" style={styles.tagline}>
            Your trusted service marketplace
          </Text>
        </View>
      </View>

      {/* Content Section */}
      <View style={styles.content}>
        <View style={styles.welcomeContent}>
          <Text variant="h2" style={styles.welcomeTitle}>
            Welcome to Vierla
          </Text>
          
          <Text variant="body" color="secondary" style={styles.welcomeDescription}>
            Connect with trusted service providers in your area. From home repairs to personal services, find exactly what you need.
          </Text>

          {/* Feature Highlights */}
          <View style={styles.features}>
            <View style={styles.feature}>
              <View style={styles.featureIcon}>
                <Text variant="h3">🔍</Text>
              </View>
              <Text variant="caption" style={styles.featureText}>
                Discover Services
              </Text>
            </View>

            <View style={styles.feature}>
              <View style={styles.featureIcon}>
                <Text variant="h3">⭐</Text>
              </View>
              <Text variant="caption" style={styles.featureText}>
                Trusted Reviews
              </Text>
            </View>

            <View style={styles.feature}>
              <View style={styles.featureIcon}>
                <Text variant="h3">💬</Text>
              </View>
              <Text variant="caption" style={styles.featureText}>
                Easy Communication
              </Text>
            </View>
          </View>
        </View>

        {/* Action Section */}
        <View style={styles.actionSection}>
          <Button
            title="Get Started"
            onPress={handleGetStarted}
            style={styles.getStartedButton}
            testID="get-started-button"
          />
          
          {/* App Version */}
          <Text variant="caption" color="secondary" style={styles.version}>
            Version 1.0.0
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primary,
  },
  header: {
    flex: 0.6,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  logoContainer: {
    alignItems: 'center',
  },
  logoPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  logoText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: colors.primary,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  tagline: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.9,
  },
  content: {
    flex: 0.4,
    backgroundColor: colors.white,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
    justifyContent: 'space-between',
  },
  welcomeContent: {
    flex: 1,
  },
  welcomeTitle: {
    textAlign: 'center',
    marginBottom: spacing.md,
    color: colors.text.primary,
  },
  welcomeDescription: {
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: spacing.xl,
  },
  features: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: spacing.lg,
  },
  feature: {
    alignItems: 'center',
    flex: 1,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.background.light,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  featureText: {
    textAlign: 'center',
    color: colors.text.secondary,
  },
  actionSection: {
    paddingBottom: spacing.lg,
  },
  getStartedButton: {
    marginBottom: spacing.md,
  },
  version: {
    textAlign: 'center',
  },
});

export default WelcomeScreen;
