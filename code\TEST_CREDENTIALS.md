# Test Credentials for Vierla Application

This document contains the test user credentials for development and testing purposes.

## Available Test Users

### Primary Test User
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: Customer
- **Status**: Active

### Demo User
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Role**: Customer
- **Status**: Active

### Debug User (Created during testing)
- **Email**: `<EMAIL>`
- **Password**: `testpass123`
- **Role**: Customer
- **Status**: Active

## Usage Instructions

### Frontend Testing
Use any of the above credentials to test the login functionality in the mobile app:

1. Open the Vierla app
2. Navigate to the login screen
3. Enter one of the email/password combinations above
4. Tap "Sign In"

### API Testing
You can also test the login API directly:

```bash
# Using PowerShell
Invoke-WebRequest -Uri "http://localhost:8000/api/auth/login/" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{"email": "<EMAIL>", "password": "demo123"}'

# Using curl (if available)
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "demo123"}'
```

## Notes

- All test users are configured with the SQLite database
- Passwords are properly hashed using Django's authentication system
- These credentials are for development/testing only
- The backend supports case-insensitive email authentication

## Creating Additional Test Users

To create additional test users, use the Django shell:

```python
# Access Django shell
python manage.py shell

# Create a new user
from authentication.models import User

user = User.objects.create_user(
    email='<EMAIL>',
    username='newuser',
    password='newpassword123',
    first_name='New',
    last_name='User',
    role='customer',  # or 'service_provider'
    is_active=True
)
```

## Troubleshooting

### "Invalid credentials" Error
- Ensure you're using the exact email and password combinations listed above
- Check that the backend server is running on `http://localhost:8000`
- Verify that the SQLite database is being used (check for `.env` file with `USE_SQLITE=true`)

### Account Locked Error
If you see an account locked error, wait a few minutes or reset the failed login attempts:

```python
# In Django shell
from authentication.models import User
user = User.objects.get(email='<EMAIL>')
user.reset_failed_login()
```

## Security Note

These are test credentials for development only. In production:
- Use strong, unique passwords
- Implement proper user registration flows
- Enable email verification
- Use environment variables for sensitive data
