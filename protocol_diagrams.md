# High-Level Agent Workflow
```mermaid
flowchart TD
    subgraph Core Autonomous TDD Cycle
        Orchestrator -- "1. Instructs to write tests" --> TestAgent;
        TestAgent -- "2. Creates failing tests" --> Orchestrator;
        Orchestrator -- "3. Instructs to write code" --> CoderAgent;
        CoderAgent -- "4. Writes code to pass tests" --> Orchestra<PERSON>;
        Orchestrator -- "5. Instructs to verify" --> VerificationAgent;
        
        VerificationAgent -- "6a. Reports PASS" --> Orchestrator;
        VerificationAgent -- "6b. Reports FAIL" --> DebugAgent;
        
        DebugAgent -- "7a. Proposes fix" --> CoderAgent;
        DebugAgent -- "7b. Declares Unrecoverable" --> Orchestrator;
    end
    
    Orchestrator -- "On Pass & Quality Check" --> NextState(Proceed to next FSM state);
    Orchestrator -- "On Unrecoverable Error" --> Recovery(Initiate Advanced Failure Recovery);
```

# Finite State Machine (FSM) Operation (Horizontal)

```mermaid
stateDiagram-v2
    direction LR

    [*] --> INITIALIZING
    INITIALIZING --> STARTUP_VERIFICATION
    STARTUP_VERIFICATION --> TASK_HIERARCHY_BUILDING : State Verified
    STARTUP_VERIFICATION --> RECOVERABLE_ERROR : Verification Failed
    TASK_HIERARCHY_BUILDING --> REPLANNING : Hierarchy Built

    REPLANNING --> TEST_WRITING : Next Epic Planned

    state TDD_Cycle {
        direction LR
        TEST_WRITING --> CODING : tests_generated
        CODING --> VERIFYING : code_generated
        CODING --> AWAITING_TEST_REFINEMENT : ambiguous_test_detected
        AWAITING_TEST_REFINEMENT --> CODING : test_refined
        AWAITING_TEST_REFINEMENT --> RECOVERABLE_ERROR : Refinement Failed
        VERIFYING --> DEBUGGING : verification_failed
        DEBUGGING --> CODING : fix_proposed
    }

    DEBUGGING --> REPLANNING : unrecoverable_error

    VERIFYING --> DOCUMENTING : verification_passed
    DOCUMENTING --> SEEDING : documentation_complete
    SEEDING --> PRE_COMMIT_REVIEW : seeding_complete
    PRE_COMMIT_REVIEW --> COMMIT_CHANGES : diff_is_clean
    
    state After_Commit_Choice <<choice>>
    COMMIT_CHANGES --> After_Commit_Choice

    After_Commit_Choice --> CHECK_TERMINALS : [infra_ready = true]
    After_Commit_Choice --> REPLANNING : [infra_ready = false]

    CHECK_TERMINALS --> REPLANNING : All Terminals OK
    CHECK_TERMINALS --> TERMINAL_DEBUGGING : Error Detected
    TERMINAL_DEBUGGING --> TERMINAL_VERIFYING : fix_applied
    TERMINAL_VERIFYING --> CHECK_TERMINALS : Verifying Fix

    state Recovery_Decision <<choice>>
    RECOVERABLE_ERROR --> Recovery_Decision : Orchestrator Autonomy
    Recovery_Decision --> REPLANNING : Decide to Replan
    Recovery_Decision --> DEBUGGING : Decide to Debug
    Recovery_Decision --> HALTED : Decide to Halt

    state End_States {
        REPLANNING --> COMPLETED : All Epics Done
        TERMINAL_DEBUGGING --> HALTED : Retry Limit Exceeded
    }
```

# Finite State Machine (FSM) Operation (Vertical)

```mermaid
stateDiagram-v2
    direction TB

    [*] --> INITIALIZING
    INITIALIZING --> STARTUP_VERIFICATION
    STARTUP_VERIFICATION --> TASK_HIERARCHY_BUILDING : State Verified
    STARTUP_VERIFICATION --> RECOVERABLE_ERROR : Verification Failed
    TASK_HIERARCHY_BUILDING --> REPLANNING : Hierarchy Built

    REPLANNING --> TEST_WRITING : Next Epic Planned

    state TDD_Cycle {
        direction TB
        TEST_WRITING --> CODING : tests_generated
        CODING --> VERIFYING : code_generated
        CODING --> AWAITING_TEST_REFINEMENT : ambiguous_test_detected
        AWAITING_TEST_REFINEMENT --> CODING : test_refined
        AWAITING_TEST_REFINEMENT --> RECOVERABLE_ERROR : Refinement Failed
        VERIFYING --> DEBUGGING : verification_failed
        DEBUGGING --> CODING : fix_proposed
    }

    DEBUGGING --> REPLANNING : unrecoverable_error

    VERIFYING --> DOCUMENTING : verification_passed
    DOCUMENTING --> SEEDING : documentation_complete
    SEEDING --> PRE_COMMIT_REVIEW : seeding_complete
    PRE_COMMIT_REVIEW --> COMMIT_CHANGES : diff_is_clean
    
    state After_Commit_Choice <<choice>>
    COMMIT_CHANGES --> After_Commit_Choice

    After_Commit_Choice --> CHECK_TERMINALS : [infra_ready = true]
    After_Commit_Choice --> REPLANNING : [infra_ready = false]

    CHECK_TERMINALS --> REPLANNING : All Terminals OK
    CHECK_TERMINALS --> TERMINAL_DEBUGGING : Error Detected
    TERMINAL_DEBUGGING --> TERMINAL_VERIFYING : fix_applied
    TERMINAL_VERIFYING --> CHECK_TERMINALS : Verifying Fix

    state Recovery_Decision <<choice>>
    RECOVERABLE_ERROR --> Recovery_Decision : Orchestrator Autonomy
    Recovery_Decision --> REPLANNING : Decide to Replan
    Recovery_Decision --> DEBUGGING : Decide to Debug
    Recovery_Decision --> HALTED : Decide to Halt

    state End_States {
        direction LR
        REPLANNING --> COMPLETED : All Epics Done
        TERMINAL_DEBUGGING --> HALTED : Retry Limit Exceeded
    }
```