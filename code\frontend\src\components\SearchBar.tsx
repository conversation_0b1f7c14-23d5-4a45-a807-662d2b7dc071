import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Keyboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Types
export interface SearchBarProps {
  value?: string;
  placeholder?: string;
  onChangeText?: (text: string) => void;
  onSearch?: (query: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onClear?: () => void;
  autoFocus?: boolean;
  showCancelButton?: boolean;
  onCancel?: () => void;
  testID?: string;
}

// Colors (following brand guidelines)
const Colors = {
  primary: {
    main: '#D81B60',
    light: '#FFD180',
    white: '#FFFFFF',
  },
  background: {
    light: '#F5F5F5',
    surface: '#FFFFFF',
  },
  text: {
    primary: '#212121',
    secondary: '#616161',
    placeholder: '#9E9E9E',
  },
  border: '#E0E0E0',
  shadow: 'rgba(0, 0, 0, 0.1)',
};

// Spacing (8pt grid)
const Spacing = {
  micro: 4,
  small: 8,
  medium: 16,
  large: 24,
};

export const SearchBar: React.FC<SearchBarProps> = ({
  value = '',
  placeholder = 'Search services...',
  onChangeText,
  onSearch,
  onFocus,
  onBlur,
  onClear,
  autoFocus = false,
  showCancelButton = false,
  onCancel,
  testID = 'search-bar',
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [internalValue, setInternalValue] = useState(value);
  const inputRef = useRef<TextInput>(null);
  const cancelButtonWidth = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  useEffect(() => {
    if (showCancelButton || isFocused) {
      Animated.timing(cancelButtonWidth, {
        toValue: 80,
        duration: 200,
        useNativeDriver: false,
      }).start();
    } else {
      Animated.timing(cancelButtonWidth, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  }, [showCancelButton, isFocused, cancelButtonWidth]);

  const handleChangeText = useCallback((text: string) => {
    setInternalValue(text);
    onChangeText?.(text);
  }, [onChangeText]);

  const handleFocus = useCallback(() => {
    setIsFocused(true);
    onFocus?.();
  }, [onFocus]);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
    onBlur?.();
  }, [onBlur]);

  const handleSearch = useCallback(() => {
    onSearch?.(internalValue);
    Keyboard.dismiss();
  }, [onSearch, internalValue]);

  const handleClear = useCallback(() => {
    setInternalValue('');
    onChangeText?.('');
    onClear?.();
    inputRef.current?.focus();
  }, [onChangeText, onClear]);

  const handleCancel = useCallback(() => {
    setInternalValue('');
    onChangeText?.('');
    onCancel?.();
    inputRef.current?.blur();
    Keyboard.dismiss();
  }, [onChangeText, onCancel]);

  const handleSubmitEditing = useCallback(() => {
    handleSearch();
  }, [handleSearch]);

  return (
    <View style={styles.container} testID={testID}>
      <View style={[
        styles.searchContainer,
        isFocused && styles.searchContainerFocused
      ]}>
        <Ionicons
          name="search"
          size={20}
          color={isFocused ? Colors.primary.main : Colors.text.secondary}
          style={styles.searchIcon}
        />
        
        <TextInput
          ref={inputRef}
          style={styles.textInput}
          value={internalValue}
          onChangeText={handleChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onSubmitEditing={handleSubmitEditing}
          placeholder={placeholder}
          placeholderTextColor={Colors.text.placeholder}
          autoFocus={autoFocus}
          returnKeyType="search"
          clearButtonMode="never" // We'll handle clear button manually
          testID={`${testID}-input`}
        />
        
        {internalValue.length > 0 && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={handleClear}
            testID={`${testID}-clear-button`}
          >
            <Ionicons
              name="close-circle"
              size={20}
              color={Colors.text.secondary}
            />
          </TouchableOpacity>
        )}
      </View>
      
      {(showCancelButton || isFocused) && (
        <Animated.View style={[styles.cancelButtonContainer, { width: cancelButtonWidth }]}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={handleCancel}
            testID={`${testID}-cancel-button`}
          >
            <Ionicons
              name="close"
              size={20}
              color={Colors.primary.main}
            />
          </TouchableOpacity>
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.medium,
    paddingVertical: Spacing.small,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.surface,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: Colors.border,
    paddingHorizontal: Spacing.medium,
    paddingVertical: Spacing.small,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  searchContainerFocused: {
    borderColor: Colors.primary.main,
    shadowOpacity: 0.2,
    elevation: 4,
  },
  searchIcon: {
    marginRight: Spacing.small,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.text.primary,
    paddingVertical: Spacing.small,
  },
  clearButton: {
    padding: Spacing.micro,
    marginLeft: Spacing.small,
  },
  cancelButtonContainer: {
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    padding: Spacing.small,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SearchBar;
