import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Switch,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../theme';
import { Service, ServiceCreateData, ServiceUpdateData, ServiceCategory } from '../../services/api';

interface ServiceFormProps {
  categories: ServiceCategory[];
  initialData?: Service;
  onSubmit: (data: ServiceCreateData | ServiceUpdateData) => void;
  onCancel: () => void;
  loading: boolean;
  submitButtonText: string;
  isEditing?: boolean;
}

interface FormData {
  name: string;
  description: string;
  short_description: string;
  category: string;
  base_price: string;
  price_type: 'fixed' | 'range';
  max_price: string;
  duration: string;
  buffer_time: string;
  requirements: string;
  preparation_instructions: string;
  is_available: boolean;
  is_popular: boolean;
}

interface FormErrors {
  [key: string]: string;
}

export const ServiceForm: React.FC<ServiceFormProps> = ({
  categories,
  initialData,
  onSubmit,
  onCancel,
  loading,
  submitButtonText,
  isEditing = false,
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    short_description: '',
    category: '',
    base_price: '',
    price_type: 'fixed',
    max_price: '',
    duration: '',
    buffer_time: '15',
    requirements: '',
    preparation_instructions: '',
    is_available: true,
    is_popular: false,
  });

  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name,
        description: initialData.description,
        short_description: initialData.short_description || '',
        category: initialData.category.id,
        base_price: initialData.base_price.toString(),
        price_type: initialData.price_type,
        max_price: initialData.max_price?.toString() || '',
        duration: initialData.duration.toString(),
        buffer_time: initialData.buffer_time.toString(),
        requirements: initialData.requirements || '',
        preparation_instructions: initialData.preparation_instructions || '',
        is_available: initialData.is_available,
        is_popular: initialData.is_popular,
      });
    }
  }, [initialData]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Service name is required';
    } else if (formData.name.trim().length < 3) {
      newErrors.name = 'Service name must be at least 3 characters';
    } else if (formData.name.length > 100) {
      newErrors.name = 'Service name cannot exceed 100 characters';
    }

    // Description validation
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.trim().length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    } else if (formData.description.length > 1000) {
      newErrors.description = 'Description cannot exceed 1000 characters';
    }

    // Category validation
    if (!formData.category) {
      newErrors.category = 'Please select a category';
    }

    // Price validation
    const basePrice = parseFloat(formData.base_price);
    if (!formData.base_price || isNaN(basePrice)) {
      newErrors.base_price = 'Base price is required';
    } else if (basePrice <= 0) {
      newErrors.base_price = 'Base price must be greater than 0';
    } else if (basePrice > 10000) {
      newErrors.base_price = 'Base price cannot exceed $10,000';
    }

    // Max price validation for range pricing
    if (formData.price_type === 'range') {
      const maxPrice = parseFloat(formData.max_price);
      if (!formData.max_price || isNaN(maxPrice)) {
        newErrors.max_price = 'Max price is required for range pricing';
      } else if (maxPrice <= basePrice) {
        newErrors.max_price = 'Max price must be greater than base price';
      }
    }

    // Duration validation
    const duration = parseInt(formData.duration);
    if (!formData.duration || isNaN(duration)) {
      newErrors.duration = 'Duration is required';
    } else if (duration <= 0) {
      newErrors.duration = 'Duration must be greater than 0';
    } else if (duration > 480) {
      newErrors.duration = 'Duration cannot exceed 8 hours (480 minutes)';
    }

    // Buffer time validation
    const bufferTime = parseInt(formData.buffer_time);
    if (formData.buffer_time && !isNaN(bufferTime)) {
      if (bufferTime < 0) {
        newErrors.buffer_time = 'Buffer time cannot be negative';
      } else if (bufferTime > 120) {
        newErrors.buffer_time = 'Buffer time cannot exceed 2 hours (120 minutes)';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors before submitting');
      return;
    }

    const submitData: ServiceCreateData | ServiceUpdateData = {
      name: formData.name.trim(),
      description: formData.description.trim(),
      short_description: formData.short_description.trim() || undefined,
      category: formData.category,
      base_price: parseFloat(formData.base_price),
      price_type: formData.price_type,
      max_price: formData.price_type === 'range' ? parseFloat(formData.max_price) : undefined,
      duration: parseInt(formData.duration),
      buffer_time: parseInt(formData.buffer_time) || 15,
      requirements: formData.requirements.trim() || undefined,
      preparation_instructions: formData.preparation_instructions.trim() || undefined,
    };

    if (isEditing) {
      (submitData as ServiceUpdateData).is_available = formData.is_available;
      (submitData as ServiceUpdateData).is_popular = formData.is_popular;
    }

    onSubmit(submitData);
  };

  const updateFormData = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const renderInput = (
    field: keyof FormData,
    label: string,
    placeholder: string,
    options?: {
      multiline?: boolean;
      keyboardType?: 'default' | 'numeric' | 'decimal-pad';
      maxLength?: number;
    }
  ) => (
    <View style={styles.inputContainer}>
      <Text style={styles.label}>{label}</Text>
      <TextInput
        style={[
          styles.input,
          options?.multiline && styles.multilineInput,
          errors[field] && styles.inputError,
        ]}
        value={formData[field] as string}
        onChangeText={(value) => updateFormData(field, value)}
        placeholder={placeholder}
        placeholderTextColor={colors.textSecondary}
        multiline={options?.multiline}
        keyboardType={options?.keyboardType}
        maxLength={options?.maxLength}
      />
      {errors[field] && <Text style={styles.errorText}>{errors[field]}</Text>}
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Basic Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Basic Information</Text>
        
        {renderInput('name', 'Service Name *', 'Enter service name', { maxLength: 100 })}
        
        {renderInput('description', 'Description *', 'Describe your service in detail', {
          multiline: true,
          maxLength: 1000,
        })}
        
        {renderInput('short_description', 'Short Description', 'Brief description for mobile display', {
          maxLength: 255,
        })}

        {/* Category Picker */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Category *</Text>
          <View style={[styles.pickerContainer, errors.category && styles.inputError]}>
            <Picker
              selectedValue={formData.category}
              onValueChange={(value) => updateFormData('category', value)}
              style={styles.picker}
            >
              <Picker.Item label="Select a category" value="" />
              {categories.map((category) => (
                <Picker.Item
                  key={category.id}
                  label={category.name}
                  value={category.id}
                />
              ))}
            </Picker>
          </View>
          {errors.category && <Text style={styles.errorText}>{errors.category}</Text>}
        </View>
      </View>

      {/* Pricing */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Pricing</Text>
        
        {/* Price Type */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Price Type</Text>
          <View style={styles.priceTypeContainer}>
            <TouchableOpacity
              style={[
                styles.priceTypeButton,
                formData.price_type === 'fixed' && styles.priceTypeButtonActive,
              ]}
              onPress={() => updateFormData('price_type', 'fixed')}
            >
              <Text style={[
                styles.priceTypeText,
                formData.price_type === 'fixed' && styles.priceTypeTextActive,
              ]}>
                Fixed Price
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.priceTypeButton,
                formData.price_type === 'range' && styles.priceTypeButtonActive,
              ]}
              onPress={() => updateFormData('price_type', 'range')}
            >
              <Text style={[
                styles.priceTypeText,
                formData.price_type === 'range' && styles.priceTypeTextActive,
              ]}>
                Price Range
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {renderInput('base_price', 'Base Price *', 'Enter base price', {
          keyboardType: 'decimal-pad',
        })}

        {formData.price_type === 'range' && renderInput(
          'max_price',
          'Maximum Price *',
          'Enter maximum price',
          { keyboardType: 'decimal-pad' }
        )}
      </View>

      {/* Duration & Timing */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Duration & Timing</Text>
        
        {renderInput('duration', 'Duration (minutes) *', 'Enter duration in minutes', {
          keyboardType: 'numeric',
        })}
        
        {renderInput('buffer_time', 'Buffer Time (minutes)', 'Time between appointments', {
          keyboardType: 'numeric',
        })}
      </View>

      {/* Additional Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Additional Information</Text>
        
        {renderInput('requirements', 'Requirements', 'Any special requirements', {
          multiline: true,
        })}
        
        {renderInput(
          'preparation_instructions',
          'Preparation Instructions',
          'Instructions for client preparation',
          { multiline: true }
        )}
      </View>

      {/* Service Settings (only for editing) */}
      {isEditing && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Service Settings</Text>
          
          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>Available for Booking</Text>
            <Switch
              value={formData.is_available}
              onValueChange={(value) => updateFormData('is_available', value)}
              trackColor={{ false: colors.border, true: colors.primaryLight }}
              thumbColor={formData.is_available ? colors.primary : colors.textSecondary}
            />
          </View>
          
          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>Mark as Popular</Text>
            <Switch
              value={formData.is_popular}
              onValueChange={(value) => updateFormData('is_popular', value)}
              trackColor={{ false: colors.border, true: colors.warningLight }}
              thumbColor={formData.is_popular ? colors.warning : colors.textSecondary}
            />
          </View>
        </View>
      )}

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={onCancel}
          disabled={loading}
        >
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.submitButton, loading && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <Text style={styles.submitButtonText}>Saving...</Text>
          ) : (
            <Text style={styles.submitButtonText}>{submitButtonText}</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: spacing.lg,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    ...typography.h3,
    color: colors.textPrimary,
    marginBottom: spacing.lg,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  label: {
    ...typography.body,
    color: colors.textPrimary,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    ...typography.body,
    color: colors.textPrimary,
    backgroundColor: colors.white,
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: colors.error,
  },
  errorText: {
    ...typography.caption,
    color: colors.error,
    marginTop: spacing.xs,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    backgroundColor: colors.white,
  },
  picker: {
    height: 50,
  },
  priceTypeContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    overflow: 'hidden',
  },
  priceTypeButton: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  priceTypeButtonActive: {
    backgroundColor: colors.primary,
  },
  priceTypeText: {
    ...typography.body,
    color: colors.textPrimary,
    fontWeight: '600',
  },
  priceTypeTextActive: {
    color: colors.white,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  switchLabel: {
    ...typography.body,
    color: colors.textPrimary,
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.xl,
    paddingTop: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: spacing.md,
    marginRight: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    ...typography.body,
    color: colors.textSecondary,
    fontWeight: '600',
  },
  submitButton: {
    flex: 1,
    paddingVertical: spacing.md,
    marginLeft: spacing.md,
    backgroundColor: colors.primary,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: colors.textSecondary,
  },
  submitButtonText: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
});
