import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth } = Dimensions.get('window');

// Types
export interface Service {
  id: string;
  name: string;
  short_description?: string;
  base_price: number;
  price_type: 'fixed' | 'hourly' | 'range' | 'consultation';
  max_price?: number;
  display_price: string;
  duration: number;
  display_duration: string;
  image?: string;
  is_popular: boolean;
  is_available: boolean;
  booking_count: number;
  provider_name: string;
  provider_rating: number;
  provider_city: string;
  category_name: string;
  category_icon: string;
  created_at: string;
}

export interface ServiceCardProps {
  service: Service;
  onPress: () => void;
  onFavorite?: () => void;
  isFavorite?: boolean;
  variant?: 'default' | 'compact' | 'featured';
  testID?: string;
}

// Colors (following brand guidelines)
const Colors = {
  primary: {
    main: '#D81B60',
    light: '#FFD180',
    white: '#FFFFFF',
  },
  background: {
    light: '#F5F5F5',
    surface: '#FFFFFF',
  },
  text: {
    primary: '#212121',
    secondary: '#616161',
  },
  status: {
    success: '#2E7D32',
    error: '#C62828',
  },
  border: '#E0E0E0',
};

// Spacing (8pt grid)
const Spacing = {
  micro: 4,
  small: 8,
  medium: 16,
  large: 24,
};

export const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  onPress,
  onFavorite,
  isFavorite = false,
  variant = 'default',
  testID,
}) => {
  const renderImage = () => {
    if (service.image) {
      return (
        <Image
          source={{ uri: service.image }}
          style={styles.image}
          testID={`${testID}-image`}
        />
      );
    }
    
    return (
      <View style={styles.placeholderImage} testID={`${testID}-placeholder`}>
        <Ionicons
          name="image-outline"
          size={32}
          color={Colors.text.secondary}
        />
      </View>
    );
  };

  const renderFavoriteButton = () => {
    if (!onFavorite) return null;

    return (
      <TouchableOpacity
        style={styles.favoriteButton}
        onPress={onFavorite}
        testID={`${testID}-favorite-button`}
      >
        <Ionicons
          name={isFavorite ? 'heart' : 'heart-outline'}
          size={20}
          color={isFavorite ? Colors.status.error : Colors.text.secondary}
        />
      </TouchableOpacity>
    );
  };

  const renderPopularBadge = () => {
    if (!service.is_popular) return null;

    return (
      <View style={styles.popularBadge} testID={`${testID}-popular-badge`}>
        <Text style={styles.popularText}>Popular</Text>
      </View>
    );
  };

  const renderRating = () => {
    return (
      <View style={styles.ratingContainer} testID={`${testID}-rating`}>
        <Ionicons name="star" size={14} color={Colors.primary.light} />
        <Text style={styles.ratingText}>
          {service.provider_rating.toFixed(1)}
        </Text>
      </View>
    );
  };

  const getCardStyle = () => {
    switch (variant) {
      case 'compact':
        return [styles.card, styles.compactCard];
      case 'featured':
        return [styles.card, styles.featuredCard];
      default:
        return styles.card;
    }
  };

  return (
    <TouchableOpacity
      style={getCardStyle()}
      onPress={onPress}
      testID={testID}
      activeOpacity={0.7}
    >
      <View style={styles.imageContainer}>
        {renderImage()}
        {renderFavoriteButton()}
        {renderPopularBadge()}
      </View>

      <View style={styles.content}>
        <Text style={styles.serviceName} numberOfLines={2} testID={`${testID}-name`}>
          {service.name}
        </Text>

        {service.short_description && (
          <Text
            style={styles.description}
            numberOfLines={2}
            testID={`${testID}-description`}
          >
            {service.short_description}
          </Text>
        )}

        <View style={styles.providerInfo} testID={`${testID}-provider-info`}>
          <Text style={styles.providerName} numberOfLines={1}>
            {service.provider_name}
          </Text>
          {renderRating()}
          <Text style={styles.providerCity} numberOfLines={1}>
            • {service.provider_city}
          </Text>
        </View>

        <View style={styles.serviceDetails} testID={`${testID}-details`}>
          <Text style={styles.price} testID={`${testID}-price`}>
            {service.display_price}
          </Text>
          <Text style={styles.duration} testID={`${testID}-duration`}>
            {service.display_duration}
          </Text>
        </View>

        {!service.is_available && (
          <View style={styles.unavailableBadge} testID={`${testID}-unavailable`}>
            <Text style={styles.unavailableText}>Currently Unavailable</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.background.surface,
    borderRadius: 12,
    marginBottom: Spacing.medium,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  compactCard: {
    marginBottom: Spacing.small,
  },
  featuredCard: {
    borderWidth: 2,
    borderColor: Colors.primary.main,
  },
  imageContainer: {
    position: 'relative',
    height: 160,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: Colors.background.light,
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteButton: {
    position: 'absolute',
    top: Spacing.small,
    right: Spacing.small,
    backgroundColor: Colors.background.surface,
    borderRadius: 20,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  popularBadge: {
    position: 'absolute',
    top: Spacing.small,
    left: Spacing.small,
    backgroundColor: Colors.primary.main,
    paddingHorizontal: Spacing.small,
    paddingVertical: Spacing.micro,
    borderRadius: 12,
  },
  popularText: {
    color: Colors.primary.white,
    fontSize: 12,
    fontWeight: '600',
  },
  content: {
    padding: Spacing.medium,
  },
  serviceName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: Spacing.micro,
  },
  description: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginBottom: Spacing.small,
    lineHeight: 20,
  },
  providerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.small,
  },
  providerName: {
    fontSize: 14,
    color: Colors.text.primary,
    fontWeight: '500',
    flex: 1,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: Spacing.small,
  },
  ratingText: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginLeft: 2,
  },
  providerCity: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  serviceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  price: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.primary.main,
  },
  duration: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  unavailableBadge: {
    marginTop: Spacing.small,
    backgroundColor: Colors.status.error,
    paddingHorizontal: Spacing.small,
    paddingVertical: Spacing.micro,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  unavailableText: {
    color: Colors.primary.white,
    fontSize: 12,
    fontWeight: '500',
  },
});

export default ServiceCard;
