import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../theme';
import { LoadingSpinner } from '../../components/common';
import { ServiceForm } from '../../components/provider/ServiceForm';
import { 
  providerServiceAPI, 
  Service, 
  ServiceUpdateData, 
  ServiceCategory 
} from '../../services/api';

interface EditServiceScreenProps {
  route: {
    params: {
      serviceId: string;
    };
  };
}

export const EditServiceScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { serviceId } = (route.params as any) || {};

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [service, setService] = useState<Service | null>(null);
  const [categories, setCategories] = useState<ServiceCategory[]>([]);

  useEffect(() => {
    if (serviceId) {
      fetchServiceAndCategories();
    } else {
      Alert.alert('Error', 'Service ID not provided');
      navigation.goBack();
    }
  }, [serviceId]);

  const fetchServiceAndCategories = async () => {
    try {
      const [serviceResponse, categoriesResponse] = await Promise.all([
        providerServiceAPI.getService(serviceId),
        providerServiceAPI.getCategories(),
      ]);
      
      setService(serviceResponse.data);
      setCategories(categoriesResponse.data);
    } catch (error) {
      console.error('Failed to fetch service or categories:', error);
      Alert.alert('Error', 'Failed to load service details');
      navigation.goBack();
    } finally {
      setInitialLoading(false);
    }
  };

  const handleSubmit = async (data: ServiceUpdateData) => {
    setLoading(true);
    try {
      await providerServiceAPI.updateService(serviceId, data);
      Alert.alert(
        'Success',
        'Service updated successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error: any) {
      console.error('Failed to update service:', error);
      
      // Handle validation errors
      if (error.response?.status === 400) {
        const errorData = error.response.data;
        let errorMessage = 'Please check the following errors:\n\n';
        
        Object.keys(errorData).forEach(field => {
          const fieldErrors = Array.isArray(errorData[field]) 
            ? errorData[field] 
            : [errorData[field]];
          errorMessage += `${field}: ${fieldErrors.join(', ')}\n`;
        });
        
        Alert.alert('Validation Error', errorMessage);
      } else {
        Alert.alert('Error', 'Failed to update service. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    Alert.alert(
      'Cancel',
      'Are you sure you want to cancel? All changes will be lost.',
      [
        { text: 'Continue Editing', style: 'cancel' },
        { text: 'Cancel', style: 'destructive', onPress: () => navigation.goBack() },
      ]
    );
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Service',
      `Are you sure you want to delete "${service?.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await providerServiceAPI.deleteService(serviceId);
              Alert.alert(
                'Success',
                'Service deleted successfully!',
                [
                  {
                    text: 'OK',
                    onPress: () => navigation.goBack(),
                  },
                ]
              );
            } catch (error) {
              Alert.alert('Error', 'Failed to delete service. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleDuplicate = async () => {
    try {
      const response = await providerServiceAPI.duplicateService(serviceId);
      Alert.alert(
        'Success',
        'Service duplicated successfully!',
        [
          {
            text: 'Edit Copy',
            onPress: () => {
              navigation.replace('EditService' as never, { serviceId: response.data.id } as never);
            },
          },
          {
            text: 'View Services',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error: any) {
      if (error.response?.status === 403) {
        Alert.alert(
          'Service Limit Reached',
          'You have reached your service limit. Please verify your account to add more services.'
        );
      } else {
        Alert.alert('Error', 'Failed to duplicate service. Please try again.');
      }
    }
  };

  if (initialLoading) {
    return <LoadingSpinner />;
  }

  if (!service) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={48} color={colors.error} />
          <Text style={styles.errorText}>Service not found</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => navigation.goBack()}>
            <Text style={styles.retryButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleCancel}>
            <Icon name="arrow-back" size={24} color={colors.textPrimary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Service</Text>
          <TouchableOpacity style={styles.menuButton} onPress={() => {
            Alert.alert(
              'Service Actions',
              'Choose an action',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Duplicate Service', onPress: handleDuplicate },
                { text: 'Delete Service', style: 'destructive', onPress: handleDelete },
              ]
            );
          }}>
            <Icon name="more-vert" size={24} color={colors.textPrimary} />
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <ServiceForm
            categories={categories}
            initialData={service}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            loading={loading}
            submitButtonText="Update Service"
            isEditing={true}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    ...typography.h2,
    color: colors.textPrimary,
    flex: 1,
    textAlign: 'center',
  },
  menuButton: {
    padding: spacing.sm,
  },
  scrollView: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  errorText: {
    ...typography.body,
    color: colors.error,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
    marginTop: spacing.lg,
  },
  retryButtonText: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
});
