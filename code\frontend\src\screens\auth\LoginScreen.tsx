/**
 * Login Screen
 * User authentication screen with email/password login
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useMutation } from '@tanstack/react-query';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Text, Button, Input, SocialButton } from '../../components';
import { authAPI, LoginRequest, SocialAuthRequest } from '../../services/api/auth';

interface LoginScreenProps {
  navigation: any; // TODO: Type this properly with navigation types
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});
  const [socialLoading, setSocialLoading] = useState<{ google: boolean; apple: boolean }>({
    google: false,
    apple: false,
  });

  // Success handler for both login types
  const handleAuthSuccess = async (response: any) => {
    try {
      // Store tokens and user data
      await AsyncStorage.multiSet([
        ['access_token', response.access],
        ['refresh_token', response.refresh],
        ['user', JSON.stringify(response.user)],
      ]);

      // Navigate to main app
      navigation.replace('Main');
    } catch (error) {
      console.error('Error storing auth data:', error);
      Alert.alert('Error', 'Failed to save login information');
    }
  };

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: (data: LoginRequest) => authAPI.login(data),
    onSuccess: handleAuthSuccess,
    onError: (error: any) => {
      console.error('Login error:', error);

      if (error.response?.status === 400) {
        const errorData = error.response.data;
        if (errorData.detail) {
          Alert.alert('Login Failed', errorData.detail);
        } else {
          setErrors(errorData);
        }
      } else if (error.response?.status === 423) {
        Alert.alert('Account Locked', 'Your account is temporarily locked due to multiple failed login attempts.');
      } else {
        Alert.alert('Error', 'An unexpected error occurred. Please try again.');
      }
    },
  });

  // Social authentication mutation
  const socialAuthMutation = useMutation({
    mutationFn: (data: SocialAuthRequest) => authAPI.socialAuth(data),
    onSuccess: handleAuthSuccess,
    onError: (error: any) => {
      console.error('Social auth error:', error);
      setSocialLoading({ google: false, apple: false });

      if (error.response?.status === 400) {
        const errorData = error.response.data;
        Alert.alert('Authentication Failed', errorData.detail || 'Invalid social authentication token');
      } else {
        Alert.alert('Error', 'Social authentication failed. Please try again.');
      }
    },
  });

  const validateForm = (): boolean => {
    const newErrors: { email?: string; password?: string } = {};

    if (!email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!password.trim()) {
      newErrors.password = 'Password is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = () => {
    if (validateForm()) {
      loginMutation.mutate({ email: email.trim(), password });
    }
  };

  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  const handleSignUp = () => {
    navigation.navigate('Register');
  };

  const handleGoogleSignIn = async () => {
    setSocialLoading(prev => ({ ...prev, google: true }));

    try {
      // TODO: Implement actual Google Sign-In
      // For now, show a placeholder message
      Alert.alert(
        'Google Sign-In',
        'Google Sign-In integration will be implemented in the next phase.',
        [{ text: 'OK', onPress: () => setSocialLoading(prev => ({ ...prev, google: false })) }]
      );
    } catch (error) {
      console.error('Google sign-in error:', error);
      setSocialLoading(prev => ({ ...prev, google: false }));
      Alert.alert('Error', 'Google sign-in failed. Please try again.');
    }
  };

  const handleAppleSignIn = async () => {
    setSocialLoading(prev => ({ ...prev, apple: true }));

    try {
      // TODO: Implement actual Apple Sign-In
      // For now, show a placeholder message
      Alert.alert(
        'Apple Sign-In',
        'Apple Sign-In integration will be implemented in the next phase.',
        [{ text: 'OK', onPress: () => setSocialLoading(prev => ({ ...prev, apple: false })) }]
      );
    } catch (error) {
      console.error('Apple sign-in error:', error);
      setSocialLoading(prev => ({ ...prev, apple: false }));
      Alert.alert('Error', 'Apple sign-in failed. Please try again.');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <Text variant="heading1" align="center" style={styles.title}>
              Welcome Back
            </Text>
            <Text variant="body" color="secondary" align="center" style={styles.subtitle}>
              Sign in to your Vierla account
            </Text>
          </View>

          <View style={styles.form}>
            <Input
              label="Email"
              value={email}
              onChangeText={setEmail}
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              leftIcon="mail"
              error={errors.email}
            />

            <Input
              label="Password"
              value={password}
              onChangeText={setPassword}
              placeholder="Enter your password"
              secureTextEntry
              leftIcon="lock-closed"
              error={errors.password}
            />

            <Button
              title="Sign In"
              onPress={handleLogin}
              loading={loginMutation.isPending}
              style={styles.loginButton}
            />

            <Button
              title="Forgot Password?"
              onPress={handleForgotPassword}
              variant="outline"
              style={styles.forgotButton}
            />

            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text variant="caption" color="secondary" style={styles.dividerText}>
                or continue with
              </Text>
              <View style={styles.dividerLine} />
            </View>

            <View style={styles.socialButtons} testID="social-buttons-container">
              <SocialButton
                provider="google"
                onPress={handleGoogleSignIn}
                loading={socialLoading.google}
                disabled={loginMutation.isPending || socialAuthMutation.isPending}
              />
              <SocialButton
                provider="apple"
                onPress={handleAppleSignIn}
                loading={socialLoading.apple}
                disabled={loginMutation.isPending || socialAuthMutation.isPending}
              />
            </View>
          </View>

          <View style={styles.footer}>
            <Text variant="body" color="secondary" align="center">
              Don't have an account?{' '}
              <Text variant="body" color="accent" onPress={handleSignUp}>
                Sign Up
              </Text>
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  header: {
    marginTop: 60,
    marginBottom: 40,
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    marginBottom: 0,
  },
  form: {
    flex: 1,
  },
  loginButton: {
    marginTop: 8,
    marginBottom: 16,
  },
  forgotButton: {
    marginBottom: 24,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E5E5EA',
  },
  dividerText: {
    marginHorizontal: 16,
  },
  socialButtons: {
    marginBottom: 32,
  },
  footer: {
    paddingBottom: 32,
  },
});
