/**
 * InitializationScreen Component
 * 
 * Handles app initialization and checks for existing user data.
 * Determines whether to show onboarding or navigate to main app.
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  ActivityIndicator,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Text } from '../../components/ui/Text';
import { colors, spacing } from '../../theme';

type InitializationScreenNavigationProp = StackNavigationProp<any, 'Initialization'>;

interface InitializationState {
  isLoading: boolean;
  error: string | null;
  progress: string;
}

export const InitializationScreen: React.FC = () => {
  const navigation = useNavigation<InitializationScreenNavigationProp>();
  const [state, setState] = useState<InitializationState>({
    isLoading: true,
    error: null,
    progress: 'Initializing app...',
  });

  useEffect(() => {
    initializeApp();
  }, []);

  const updateProgress = (message: string) => {
    setState(prev => ({ ...prev, progress: message }));
  };

  const initializeApp = async () => {
    try {
      // Step 1: Check app initialization status
      updateProgress('Checking app status...');
      await new Promise(resolve => setTimeout(resolve, 800));

      // Step 2: Check for existing user data
      updateProgress('Checking user data...');
      const existingUserData = await checkExistingUserData();
      await new Promise(resolve => setTimeout(resolve, 600));

      // Step 3: Check onboarding completion
      updateProgress('Loading preferences...');
      const onboardingCompleted = await checkOnboardingStatus();
      await new Promise(resolve => setTimeout(resolve, 400));

      // Step 4: Determine navigation path
      updateProgress('Finalizing setup...');
      await new Promise(resolve => setTimeout(resolve, 500));

      // Navigation logic
      if (existingUserData && onboardingCompleted) {
        // User has completed onboarding and has data - go to main app
        navigation.reset({
          index: 0,
          routes: [{ name: 'Auth' }], // Will redirect to Main if authenticated
        });
      } else if (onboardingCompleted) {
        // Onboarding completed but no user data - go to auth
        navigation.reset({
          index: 0,
          routes: [{ name: 'Auth' }],
        });
      } else {
        // First time user - start onboarding
        navigation.navigate('RoleSelection');
      }

    } catch (error) {
      console.error('Initialization error:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to initialize app. Please try again.',
      }));

      // Fallback to role selection after error
      setTimeout(() => {
        navigation.navigate('RoleSelection');
      }, 2000);
    }
  };

  const checkExistingUserData = async (): Promise<boolean> => {
    try {
      const accessToken = await AsyncStorage.getItem('access_token');
      const user = await AsyncStorage.getItem('user');
      return !!(accessToken && user);
    } catch (error) {
      console.error('Error checking user data:', error);
      return false;
    }
  };

  const checkOnboardingStatus = async (): Promise<boolean> => {
    try {
      const onboardingStatus = await AsyncStorage.getItem('onboarding_completed');
      return onboardingStatus === 'true';
    } catch (error) {
      console.error('Error checking onboarding status:', error);
      return false;
    }
  };

  const handleRetry = () => {
    setState({
      isLoading: true,
      error: null,
      progress: 'Retrying initialization...',
    });
    initializeApp();
  };

  if (state.error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
        
        <View style={styles.content}>
          <View style={styles.errorContainer}>
            <Text variant="h3" style={styles.errorTitle}>
              Initialization Failed
            </Text>
            
            <Text variant="body" color="secondary" style={styles.errorMessage}>
              {state.error}
            </Text>
            
            <View style={styles.retryButton}>
              <Text 
                variant="button" 
                color="primary" 
                onPress={handleRetry}
                style={styles.retryText}
              >
                Tap to Retry
              </Text>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      
      <View style={styles.content}>
        <View style={styles.loadingContainer}>
          {/* App Logo */}
          <View style={styles.logoContainer}>
            <View style={styles.logoPlaceholder}>
              <Text variant="h1" style={styles.logoText}>
                V
              </Text>
            </View>
            
            <Text variant="h2" style={styles.appName}>
              Vierla
            </Text>
          </View>

          {/* Loading Indicator */}
          <View style={styles.loadingSection}>
            <ActivityIndicator 
              size="large" 
              color={colors.primary} 
              testID="loading-indicator"
            />
            
            <Text variant="body" color="secondary" style={styles.progressText}>
              {state.progress}
            </Text>
          </View>

          {/* Loading Steps Indicator */}
          <View style={styles.stepsContainer}>
            <View style={styles.stepsRow}>
              <View style={[styles.step, styles.stepActive]} />
              <View style={[styles.step, state.progress.includes('user') ? styles.stepActive : styles.stepInactive]} />
              <View style={[styles.step, state.progress.includes('preferences') ? styles.stepActive : styles.stepInactive]} />
              <View style={[styles.step, state.progress.includes('Finalizing') ? styles.stepActive : styles.stepInactive]} />
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  loadingContainer: {
    alignItems: 'center',
    width: '100%',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl * 2,
  },
  logoPlaceholder: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  logoText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.white,
  },
  appName: {
    color: colors.text.primary,
  },
  loadingSection: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  progressText: {
    marginTop: spacing.md,
    textAlign: 'center',
  },
  stepsContainer: {
    marginTop: spacing.lg,
  },
  stepsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  step: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: spacing.xs,
  },
  stepActive: {
    backgroundColor: colors.primary,
  },
  stepInactive: {
    backgroundColor: colors.border,
  },
  errorContainer: {
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  errorTitle: {
    color: colors.error,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  errorMessage: {
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: spacing.xl,
  },
  retryButton: {
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  retryText: {
    textAlign: 'center',
  },
});

export default InitializationScreen;
