import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';

// Mock navigation
const mockNavigation = {
  navigate: jest.fn(),
  goBack: jest.fn(),
  setOptions: jest.fn(),
};

// Mock dashboard data
const mockDashboardData = {
  metrics: {
    totalServices: 5,
    activeServices: 4,
    totalBookings: 25,
    pendingBookings: 3,
    totalRevenue: 1250.00,
    averageRating: 4.8,
  },
  recentServices: [
    {
      id: 'service-1',
      name: 'Premium Haircut',
      base_price: 75.00,
      duration: 60,
      is_active: true,
      booking_count: 15,
    },
    {
      id: 'service-2',
      name: 'Hair Color',
      base_price: 150.00,
      duration: 120,
      is_active: true,
      booking_count: 8,
    },
  ],
  recentBookings: [
    {
      id: 'booking-1',
      service_name: 'Premium Haircut',
      customer_name: '<PERSON>',
      scheduled_date: '2024-01-15',
      scheduled_time: '10:00',
      status: 'confirmed',
      total_amount: 75.00,
    },
    {
      id: 'booking-2',
      service_name: 'Hair Color',
      customer_name: '<PERSON>',
      scheduled_date: '2024-01-16',
      scheduled_time: '14:00',
      status: 'pending',
      total_amount: 150.00,
    },
  ],
};

// Mock ProviderDashboard component
interface ProviderDashboardProps {
  data: typeof mockDashboardData;
  onRefresh?: () => void;
  onNavigateToServices?: () => void;
  onNavigateToBookings?: () => void;
  onNavigateToAnalytics?: () => void;
  isLoading?: boolean;
  testID?: string;
}

const ProviderDashboard: React.FC<ProviderDashboardProps> = ({
  data,
  onRefresh,
  onNavigateToServices,
  onNavigateToBookings,
  onNavigateToAnalytics,
  isLoading = false,
  testID = 'provider-dashboard',
}) => {
  const { metrics, recentServices, recentBookings } = data;

  return (
    <div testID={testID}>
      {/* Header */}
      <div testID="dashboard-header">
        <h1 testID="dashboard-title">Provider Dashboard</h1>
        <button
          testID="refresh-button"
          onClick={onRefresh}
          disabled={isLoading}
        >
          {isLoading ? 'Loading...' : 'Refresh'}
        </button>
      </div>

      {/* Metrics Grid */}
      <div testID="metrics-grid">
        <div testID="metric-total-services">
          <span testID="metric-label">Total Services</span>
          <span testID="metric-value">{metrics.totalServices}</span>
        </div>
        <div testID="metric-active-services">
          <span testID="metric-label">Active Services</span>
          <span testID="metric-value">{metrics.activeServices}</span>
        </div>
        <div testID="metric-total-bookings">
          <span testID="metric-label">Total Bookings</span>
          <span testID="metric-value">{metrics.totalBookings}</span>
        </div>
        <div testID="metric-pending-bookings">
          <span testID="metric-label">Pending Bookings</span>
          <span testID="metric-value">{metrics.pendingBookings}</span>
        </div>
        <div testID="metric-total-revenue">
          <span testID="metric-label">Total Revenue</span>
          <span testID="metric-value">${metrics.totalRevenue.toFixed(2)}</span>
        </div>
        <div testID="metric-average-rating">
          <span testID="metric-label">Average Rating</span>
          <span testID="metric-value">{metrics.averageRating.toFixed(1)}</span>
        </div>
      </div>

      {/* Quick Actions */}
      <div testID="quick-actions">
        <h2 testID="quick-actions-title">Quick Actions</h2>
        <div testID="action-buttons">
          <button
            testID="manage-services-button"
            onClick={onNavigateToServices}
          >
            Manage Services
          </button>
          <button
            testID="view-bookings-button"
            onClick={onNavigateToBookings}
          >
            View Bookings
          </button>
          <button
            testID="view-analytics-button"
            onClick={onNavigateToAnalytics}
          >
            View Analytics
          </button>
        </div>
      </div>

      {/* Recent Services */}
      <div testID="recent-services-section">
        <h2 testID="recent-services-title">Recent Services</h2>
        <div testID="recent-services-list">
          {recentServices.map(service => (
            <div key={service.id} testID={`service-item-${service.id}`}>
              <span testID="service-name">{service.name}</span>
              <span testID="service-price">${service.base_price.toFixed(2)}</span>
              <span testID="service-duration">{service.duration}min</span>
              <span testID="service-bookings">{service.booking_count} bookings</span>
              <span testID="service-status">
                {service.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Bookings */}
      <div testID="recent-bookings-section">
        <h2 testID="recent-bookings-title">Recent Bookings</h2>
        <div testID="recent-bookings-list">
          {recentBookings.map(booking => (
            <div key={booking.id} testID={`booking-item-${booking.id}`}>
              <span testID="booking-service">{booking.service_name}</span>
              <span testID="booking-customer">{booking.customer_name}</span>
              <span testID="booking-date">{booking.scheduled_date}</span>
              <span testID="booking-time">{booking.scheduled_time}</span>
              <span testID="booking-status">{booking.status}</span>
              <span testID="booking-amount">${booking.total_amount.toFixed(2)}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

describe('ProviderDashboard', () => {
  const defaultProps = {
    data: mockDashboardData,
    onRefresh: jest.fn(),
    onNavigateToServices: jest.fn(),
    onNavigateToBookings: jest.fn(),
    onNavigateToAnalytics: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders dashboard components correctly', () => {
      const { getByTestId } = render(<ProviderDashboard {...defaultProps} />);

      expect(getByTestId('provider-dashboard')).toBeTruthy();
      expect(getByTestId('dashboard-header')).toBeTruthy();
      expect(getByTestId('dashboard-title')).toBeTruthy();
      expect(getByTestId('metrics-grid')).toBeTruthy();
      expect(getByTestId('quick-actions')).toBeTruthy();
      expect(getByTestId('recent-services-section')).toBeTruthy();
      expect(getByTestId('recent-bookings-section')).toBeTruthy();
    });

    it('displays metrics correctly', () => {
      const { getByTestId } = render(<ProviderDashboard {...defaultProps} />);

      expect(getByTestId('metric-total-services').textContent).toContain('5');
      expect(getByTestId('metric-active-services').textContent).toContain('4');
      expect(getByTestId('metric-total-bookings').textContent).toContain('25');
      expect(getByTestId('metric-pending-bookings').textContent).toContain('3');
      expect(getByTestId('metric-total-revenue').textContent).toContain('$1250.00');
      expect(getByTestId('metric-average-rating').textContent).toContain('4.8');
    });

    it('renders recent services list', () => {
      const { getByTestId } = render(<ProviderDashboard {...defaultProps} />);

      expect(getByTestId('service-item-service-1')).toBeTruthy();
      expect(getByTestId('service-item-service-2')).toBeTruthy();
      
      // Check first service details
      const firstService = getByTestId('service-item-service-1');
      expect(firstService.textContent).toContain('Premium Haircut');
      expect(firstService.textContent).toContain('$75.00');
      expect(firstService.textContent).toContain('60min');
      expect(firstService.textContent).toContain('15 bookings');
      expect(firstService.textContent).toContain('Active');
    });

    it('renders recent bookings list', () => {
      const { getByTestId } = render(<ProviderDashboard {...defaultProps} />);

      expect(getByTestId('booking-item-booking-1')).toBeTruthy();
      expect(getByTestId('booking-item-booking-2')).toBeTruthy();
      
      // Check first booking details
      const firstBooking = getByTestId('booking-item-booking-1');
      expect(firstBooking.textContent).toContain('Premium Haircut');
      expect(firstBooking.textContent).toContain('Sarah Johnson');
      expect(firstBooking.textContent).toContain('2024-01-15');
      expect(firstBooking.textContent).toContain('10:00');
      expect(firstBooking.textContent).toContain('confirmed');
      expect(firstBooking.textContent).toContain('$75.00');
    });
  });

  describe('Interactions', () => {
    it('calls onRefresh when refresh button is pressed', () => {
      const { getByTestId } = render(<ProviderDashboard {...defaultProps} />);

      fireEvent.click(getByTestId('refresh-button'));

      expect(defaultProps.onRefresh).toHaveBeenCalledTimes(1);
    });

    it('calls onNavigateToServices when manage services button is pressed', () => {
      const { getByTestId } = render(<ProviderDashboard {...defaultProps} />);

      fireEvent.click(getByTestId('manage-services-button'));

      expect(defaultProps.onNavigateToServices).toHaveBeenCalledTimes(1);
    });

    it('calls onNavigateToBookings when view bookings button is pressed', () => {
      const { getByTestId } = render(<ProviderDashboard {...defaultProps} />);

      fireEvent.click(getByTestId('view-bookings-button'));

      expect(defaultProps.onNavigateToBookings).toHaveBeenCalledTimes(1);
    });

    it('calls onNavigateToAnalytics when view analytics button is pressed', () => {
      const { getByTestId } = render(<ProviderDashboard {...defaultProps} />);

      fireEvent.click(getByTestId('view-analytics-button'));

      expect(defaultProps.onNavigateToAnalytics).toHaveBeenCalledTimes(1);
    });
  });

  describe('Loading State', () => {
    it('shows loading state correctly', () => {
      const props = {
        ...defaultProps,
        isLoading: true,
      };
      const { getByTestId } = render(<ProviderDashboard {...props} />);

      expect(getByTestId('refresh-button').textContent).toBe('Loading...');
      expect((getByTestId('refresh-button') as HTMLButtonElement).disabled).toBe(true);
    });
  });

  describe('Empty States', () => {
    it('handles empty services list', () => {
      const emptyData = {
        ...mockDashboardData,
        recentServices: [],
      };
      const props = {
        ...defaultProps,
        data: emptyData,
      };
      const { getByTestId, queryByTestId } = render(<ProviderDashboard {...props} />);

      expect(getByTestId('recent-services-list')).toBeTruthy();
      expect(queryByTestId('service-item-service-1')).toBeFalsy();
    });

    it('handles empty bookings list', () => {
      const emptyData = {
        ...mockDashboardData,
        recentBookings: [],
      };
      const props = {
        ...defaultProps,
        data: emptyData,
      };
      const { getByTestId, queryByTestId } = render(<ProviderDashboard {...props} />);

      expect(getByTestId('recent-bookings-list')).toBeTruthy();
      expect(queryByTestId('booking-item-booking-1')).toBeFalsy();
    });
  });

  describe('Accessibility', () => {
    it('has proper testIDs for accessibility testing', () => {
      const { getByTestId } = render(<ProviderDashboard {...defaultProps} />);

      expect(getByTestId('provider-dashboard')).toBeTruthy();
      expect(getByTestId('dashboard-header')).toBeTruthy();
      expect(getByTestId('metrics-grid')).toBeTruthy();
      expect(getByTestId('quick-actions')).toBeTruthy();
      expect(getByTestId('recent-services-section')).toBeTruthy();
      expect(getByTestId('recent-bookings-section')).toBeTruthy();
    });

    it('accepts custom testID', () => {
      const props = {
        ...defaultProps,
        testID: 'custom-dashboard',
      };
      const { getByTestId } = render(<ProviderDashboard {...props} />);

      expect(getByTestId('custom-dashboard')).toBeTruthy();
    });
  });
});
