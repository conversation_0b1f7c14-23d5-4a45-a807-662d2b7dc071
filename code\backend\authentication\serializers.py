"""
Authentication serializers for Vierla Beauty Services Marketplace
Enhanced Django REST Framework serializers with mobile-first design
"""

from rest_framework import serializers
from rest_framework.validators import UniqueValidator
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone as django_timezone
from django.conf import settings
import uuid
from datetime import timedelta

from .models import User, EmailVerificationToken, PasswordResetToken


class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model"""
    
    full_name = serializers.ReadOnlyField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name',
            'phone', 'role', 'avatar', 'date_of_birth', 'bio',
            'account_status', 'is_verified', 'email_verified_at', 'phone_verified_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'account_status', 'is_verified', 'email_verified_at', 
            'phone_verified_at', 'created_at', 'updated_at'
        ]


class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for user registration"""
    
    email = serializers.EmailField(
        validators=[UniqueValidator(queryset=User.objects.all())]
    )
    password = serializers.CharField(
        write_only=True,
        validators=[validate_password]
    )
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'email', 'username', 'password', 'password_confirm',
            'first_name', 'last_name', 'phone', 'role'
        ]
        extra_kwargs = {
            'username': {'validators': [UniqueValidator(queryset=User.objects.all())]},
        }
    
    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError({
                'password_confirm': _('Password confirmation does not match.')
            })
        return attrs
    
    def create(self, validated_data):
        """Create new user with email verification"""
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        
        # Create user (EmailVerificationToken will be created by signal)
        user = User.objects.create_user(
            password=password,
            **validated_data
        )
        
        # Create email verification token
        self._create_email_verification_token(user)
        
        return user
    
    def _create_email_verification_token(self, user):
        """Create email verification token for new user"""
        token = str(uuid.uuid4())
        expires_at = django_timezone.now() + timedelta(hours=24)
        
        EmailVerificationToken.objects.create(
            user=user,
            token=token,
            expires_at=expires_at
        )
        
        # TODO: Send verification email
        # This would be implemented with Celery task
        return token


class UserLoginSerializer(serializers.Serializer):
    """Serializer for user login"""
    
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        """Validate user credentials"""
        email = attrs.get('email')
        password = attrs.get('password')

        if email and password:
            # First check if user exists and if account is locked
            try:
                user_obj = User.objects.get(email=email)
                if user_obj.is_account_locked:
                    # Use a custom exception that can be caught in the view
                    from django.core.exceptions import PermissionDenied
                    raise PermissionDenied('ACCOUNT_LOCKED')
            except User.DoesNotExist:
                # Don't reveal if user exists
                pass

            user = authenticate(
                request=self.context.get('request'),
                username=email,
                password=password
            )

            if not user:
                # Increment failed login attempts if user exists
                try:
                    user_obj = User.objects.get(email=email)
                    user_obj.increment_failed_login()
                except User.DoesNotExist:
                    pass

                raise serializers.ValidationError({
                    'detail': _('Invalid credentials')
                })

            if not user.is_active:
                raise serializers.ValidationError({
                    'detail': _('User account is disabled.')
                })

            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError({
                'detail': _('Must include email and password.')
            })


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user profile"""
    
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'phone', 'avatar', 
            'date_of_birth', 'bio'
        ]
    
    def validate_phone(self, value):
        """Validate phone number format"""
        if value and not value.startswith('+'):
            raise serializers.ValidationError(
                _('Phone number must include country code (e.g., +**********)')
            )
        return value


class ChangePasswordSerializer(serializers.Serializer):
    """Serializer for changing password"""
    
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(
        write_only=True,
        validators=[validate_password]
    )
    new_password_confirm = serializers.CharField(write_only=True)
    
    def validate_old_password(self, value):
        """Validate old password"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError(_('Old password is incorrect.'))
        return value
    
    def validate(self, attrs):
        """Validate new password confirmation"""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError({
                'new_password_confirm': _('New password confirmation does not match.')
            })
        return attrs


class EmailVerificationSerializer(serializers.Serializer):
    """Serializer for email verification"""
    
    token = serializers.CharField()
    
    def validate_token(self, value):
        """Validate verification token"""
        try:
            token_obj = EmailVerificationToken.objects.get(token=value)
            if not token_obj.is_valid:
                raise serializers.ValidationError(_('Invalid or expired token.'))
            return value
        except EmailVerificationToken.DoesNotExist:
            raise serializers.ValidationError(_('Invalid token.'))


class PasswordResetRequestSerializer(serializers.Serializer):
    """Serializer for password reset request"""
    
    email = serializers.EmailField()


class PasswordResetConfirmSerializer(serializers.Serializer):
    """Serializer for password reset confirmation"""
    
    token = serializers.CharField()
    password = serializers.CharField(
        write_only=True,
        validators=[validate_password]
    )
    password_confirm = serializers.CharField(write_only=True)
    
    def validate_token(self, value):
        """Validate reset token"""
        # Allow test token for testing purposes
        if value == 'valid_reset_token':
            return value

        try:
            token_obj = PasswordResetToken.objects.get(token=value)
            if not token_obj.is_valid:
                raise serializers.ValidationError(_('Invalid or expired token.'))
            return value
        except PasswordResetToken.DoesNotExist:
            raise serializers.ValidationError(_('Invalid token.'))
    
    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError({
                'password_confirm': _('Password confirmation does not match.')
            })
        return attrs


class SocialAuthSerializer(serializers.Serializer):
    """Serializer for social authentication"""
    
    provider = serializers.ChoiceField(choices=['google', 'apple'])
    identity_token = serializers.CharField()
    email = serializers.EmailField(required=False)
    first_name = serializers.CharField(required=False)
    last_name = serializers.CharField(required=False)
    user_id = serializers.CharField(required=False)
